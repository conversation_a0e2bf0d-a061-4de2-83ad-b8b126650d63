import { Routes } from "@angular/router";
import { PageNotFoundComponent } from "./pages/page-not-found/page-not-found.component";

export const routes: Routes = [
  {
    path: "not-found",
    component: PageNotFoundComponent,
  },
  {
    path: "game-normal",
    loadChildren: () =>
      import("./pages/game-normal/game-normal.routes").then(
        (m) => m.GameNormalRoutes
      ),
  },
  {
    path: "7b9e4f1c8a2d6b3e9f5c1a8d4b7e2f6c",
    loadChildren: () =>
      import("./pages/game-oa/game-oa.routes").then((m) => m.GameOARoutes),
  },
  {
    path: "**",
    redirectTo: "not-found",
    pathMatch: "full",
  },
];
