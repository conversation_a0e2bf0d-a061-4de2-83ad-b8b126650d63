import { Routes } from "@angular/router";
import { PageNotFoundComponent } from "./pages/page-not-found/page-not-found.component";

// a3d5c8f2e9b7d4c1a6f8e2b9c5d7a1f4 - normal
// 7b9e4f1c8a2d6b3e9f5c1a8d4b7e2f6c - oa

export const routes: Routes = [
  {
    path: "not-found",
    component: PageNotFoundComponent,
  },
  {
    path: "a3d5c8f2e9b7d4c1a6f8e2b9c5d7a1f4",
    loadChildren: () =>
      import("./pages/game-normal/game-normal.routes").then(
        (m) => m.GameNormalRoutes
      ),
  },
  {
    path: "game-oa",
    loadChildren: () =>
      import("./pages/game-oa/game-oa.routes").then((m) => m.GameOARoutes),
  },
  {
    path: "**",
    redirectTo: "not-found",
    pathMatch: "full",
  },
];
