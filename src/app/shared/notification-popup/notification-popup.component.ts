import { Component, Input, Output, EventEmitter } from "@angular/core";
import { CommonModule } from "@angular/common";

@Component({
  selector: "app-notification-popup",
  standalone: true,
  imports: [CommonModule],
  templateUrl: "./notification-popup.component.html",
  styleUrl: "./notification-popup.component.scss",
})
export class NotificationPopupComponent {
  @Input() message: string = "";
  @Input() isVisible: boolean = false;
  @Output() close = new EventEmitter<void>();

  onClose(): void {
    this.close.emit();
  }

  onOverlayClick(): void {
    this.onClose();
  }
}
