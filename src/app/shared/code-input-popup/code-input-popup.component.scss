::placeholder {
  color: var(--main-white);
}
.code-popup {
  background: var(--main-white);
  border-radius: 10px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 90%;
  max-width: 400px;
  align-items: center;
  justify-content: center;
  gap: 20px;
  .title {
    width: 65%;
    height: auto;
    object-fit: contain;
  }
  .vqmm {
    width: 100%;
    height: auto;
    object-fit: contain;
  }
  .head-text {
    color: var(--main-black);
    font-size: 14px;
    font-weight: 600;
    line-height: 1.5;
    text-align: center;
    text-transform: uppercase;
    width: 105%;
  }
  .input-code {
    width: 90%;
    height: 48px;
    padding: 0 16px;
    border-radius: 12px;
    font-size: 16px;
    border: none;
    outline: none;
    background: var(--main-red);
    color: var(--main-white);
    transition: all 0.2s ease;
  }
  .form-actions {
    width: 90%;
    .form-actions-btn {
      width: 100%;
      object-fit: contain;
    }
  }
}
