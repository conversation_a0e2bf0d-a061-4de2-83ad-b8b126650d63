import { Injectable } from "@angular/core";
import { BehaviorSubject } from "rxjs";

export interface PopupState {
  type: "notification" | "code" | "loading" | null;
  message?: string;
}

@Injectable({
  providedIn: "root",
})
export class PopupService {
  private popupState = new BehaviorSubject<PopupState>({ type: null });

  popupState$ = this.popupState.asObservable();

  showNotification(message: string) {
    this.popupState.next({ type: "notification", message });
  }

  showCodeInput() {
    this.popupState.next({ type: "code" });
  }

  showLoading(message?: string) {
    this.popupState.next({ type: "loading", message });
  }

  closePopup() {
    this.popupState.next({ type: null });
  }
}
