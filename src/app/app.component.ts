import { Component } from "@angular/core";
import { RouterOutlet } from "@angular/router";
import { NotificationPopupComponent } from "./shared/notification-popup/notification-popup.component";
import { LoadingPopupComponent } from "./shared/loading-popup/loading-popup.component";
import { CommonModule } from "@angular/common";
import { PopupService } from "./helpers/popup.service";

@Component({
  selector: "app-root",
  standalone: true,
  imports: [RouterOutlet, CommonModule, NotificationPopupComponent, LoadingPopupComponent],
  templateUrl: "./app.component.html",
  styleUrl: "./app.component.scss",
})
export class AppComponent {
  title = "FE-DECUA-245";
  constructor(public popupService: PopupService) {}

  closePopup() {
    this.popupService.closePopup();
  }
}
