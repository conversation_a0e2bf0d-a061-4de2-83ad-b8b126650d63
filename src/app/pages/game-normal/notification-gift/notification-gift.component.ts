import { Component, OnInit } from "@angular/core";
import { GIFTS_CODE } from "../../../constants/gift";
import { Router } from "@angular/router";
import { UtilsService } from "../../../helpers/utils.service";
import { CommonModule } from "@angular/common";
import { PopupService } from "../../../helpers/popup.service";
import { CodeInputPopupComponent } from "../../../shared/code-input-popup/code-input-popup.component";
import { ApiService } from "../../../helpers/api.service";

@Component({
  selector: "app-notification-gift",
  standalone: true,
  imports: [CommonModule, CodeInputPopupComponent],
  templateUrl: "./notification-gift.component.html",
  styleUrl: "./notification-gift.component.scss",
})
export class NotificationGiftComponent implements OnInit {
  nameGift: string = "";
  price: string = "";

  nameUser: string = "";
  branch: string = "";
  phone: string = "";

  constructor(
    private router: Router,
    private utils: UtilsService,
    public popupService: PopupService,
    private apiService: ApiService
  ) {}

  ngOnInit() {
    const PHONE = localStorage.getItem("phone");
    const AWARD = localStorage.getItem("award");
    const NAME_USER = localStorage.getItem("nameUser");
    const BRANCH = localStorage.getItem("branch");
    if (PHONE && AWARD && NAME_USER && BRANCH) {
      const GIFT = GIFTS_CODE.find((gift) => gift.uid === AWARD);
      if (GIFT) {
        this.nameGift = GIFT.name || "";
        this.price = GIFT.price || "";
        this.nameUser = NAME_USER || "";
        this.branch = BRANCH || "";
        this.phone = PHONE;
      }
    } else {
      this.router.navigate(["/" + this.utils.getGameKey()]).then(() => {
        this.utils.clearLocalStorage();
      });
    }
  }

  regain() {
    this.popupService.showCodeInput();
  }

  onCodeSubmit(event: any) {
    if (!event) {
      this.popupService.showNotification("Mã code không hợp lệ!");
      return;
    }

    const formData = {
      name: this.nameUser,
      phone: this.phone,
      storeItem: this.branch,
      code: event,
    };

    this.popupService.showLoading();

    this.apiService
      .verifyInformationGameNormal("get-award-anonymous-month", formData)
      .subscribe({
        next: (response: any) => {
          switch (parseInt(response.status)) {
            case 1:
              this.router
                .navigate(["/" + this.utils.getGameKey() + "/game-play"])
                .then((success) => {
                  if (success) {
                    const data = response.giftCode[0];
                    if (!data) {
                      this.popupService.closePopup();
                      this.popupService.showNotification(
                        "Thông tin không hợp lệ!"
                      );
                      return;
                    }
                    localStorage.setItem("award", data.voucher);
                    localStorage.setItem("code", data.code);
                    localStorage.setItem("name", data.name);
                    localStorage.setItem("phone", formData.phone);
                    localStorage.setItem("isSpin", "can");
                    localStorage.setItem("nameUser", this.nameUser);
                    localStorage.setItem("branch", this.branch.toString());
                    this.popupService.closePopup();
                  } else {
                    this.popupService.closePopup();
                    this.popupService.showNotification(
                      "Chuyển trang không thành công!"
                    );
                    return;
                  }
                })
                .catch((error) => {
                  this.popupService.closePopup();
                  this.popupService.showNotification(
                    "Chuyển trang không thành công!"
                  );
                  return;
                });
              break;
            case -2:
              this.popupService.closePopup();
              this.popupService.showNotification("Thông tin không hợp lệ!");
              break;
            case -4:
              const data = response.giftCode[0];
              if (!data) {
                this.popupService.closePopup();
                this.popupService.showNotification("Thông tin không hợp lệ!");
                return;
              }
              if (data.status != 1) {
                this.popupService.closePopup();
                this.popupService.showNotification(
                  "Số điện thoại đã hoàn thành chương trình trước đó!"
                );
                return;
              }

              this.router
                .navigate(["/" + this.utils.getGameKey() + "/game-play"])
                .then((success) => {
                  if (success) {
                    this.popupService.closePopup();
                    localStorage.setItem("award", data.voucher);
                    localStorage.setItem("code", data.code);
                    localStorage.setItem("name", data.name);
                    localStorage.setItem("phone", formData.phone);
                    localStorage.setItem("isSpin", "can");
                  } else {
                    this.popupService.closePopup();
                    this.popupService.showNotification(
                      "Chuyển trang không thành công!"
                    );
                    return;
                  }
                })
                .catch((error) => {
                  this.popupService.closePopup();
                  this.popupService.showNotification(
                    "Chuyển trang không thành công!"
                  );
                  return;
                });
              break;
            default:
              this.popupService.closePopup();
              this.popupService.showNotification("Thông tin không hợp lệ!");
              break;
          }
        },
        error: (error: any) => {
          this.popupService.closePopup();
          this.popupService.showNotification("Hệ thống đang bảo trì!");
        },
        complete: () => {},
      });
  }
}
