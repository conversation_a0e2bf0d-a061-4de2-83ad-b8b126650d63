import { Component, OnInit } from "@angular/core";
import { GIFTS_CODE } from "../../../constants/gift";
import { Router } from "@angular/router";
import { UtilsService } from "../../../helpers/utils.service";
import { CommonModule } from "@angular/common";

@Component({
  selector: "app-notification-gift",
  standalone: true,
  imports: [CommonModule],
  templateUrl: "./notification-gift.component.html",
  styleUrl: "./notification-gift.component.scss",
})
export class NotificationGiftComponent implements OnInit {
  nameGift: string = "";
  price: string = "";
  constructor(private router: Router, private utils: UtilsService) {}

  ngOnInit() {
    const PHONE = localStorage.getItem("phone");
    const AWARD = localStorage.getItem("award");
    if (PHONE && AWARD) {
      const GIFT = GIFTS_CODE.find((gift) => gift.uid === AWARD);
      if (GIFT) {
        this.nameGift = GIFT.name || "";
        this.price = GIFT.price || "";
      }
    } else {
      this.router.navigate(["/" + this.utils.getGameKey()]).then(() => {
        this.utils.clearLocalStorage();
      });
    }
  }
}
