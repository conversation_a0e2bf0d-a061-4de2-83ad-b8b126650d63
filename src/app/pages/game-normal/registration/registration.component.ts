import { Component } from "@angular/core";
import { BRANCH_OPTION, BranchOption } from "../../../constants/branch-option";
import { CommonModule } from "@angular/common";
import { Router } from "@angular/router";
import { PopupService } from "../../../helpers/popup.service";
import { FormsModule } from "@angular/forms";
import { UtilsService } from "../../../helpers/utils.service";
import { ApiService } from "../../../helpers/api.service";

@Component({
  selector: "app-registration",
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: "./registration.component.html",
  styleUrls: ["./registration.component.scss"],
})
export class RegistrationComponent {
  name: string = "";
  phone: string = "";
  branch: number = 0;
  code: string = "";

  branchOption: BranchOption[] = BRANCH_OPTION;

  isClick: boolean = false;

  constructor(
    private router: Router,
    private popupService: PopupService,
    private utils: UtilsService,
    private apiService: ApiService
  ) {}

  phoneChange(event: string) {
    this.phone = this.utils.keyupPhoneChange(event);
  }

  onSubmit() {
    // Lấy dữ liệu từ các input
    const formData = {
      name: this.name,
      phone: this.phone,
      storeItem: this.branch,
      code: this.code,
    };

    this.popupService.showLoading();

    // Xử lý validate nếu cần
    if (
      !formData.name ||
      !formData.phone ||
      !formData.storeItem ||
      !formData.code
    ) {
      this.popupService.closePopup();
      this.popupService.showNotification("Vui lòng điền đầy đủ thông tin!");
      return;
    }

    const phoneCheck = this.utils.validatePhone(this.phone);
    if (phoneCheck.status !== 0) {
      this.popupService.closePopup();
      this.popupService.showNotification("Số diện thoại không hợp lệ!");
      return;
    }

    formData.phone = phoneCheck.phone;
    this.apiService
      .verifyInformationGameNormal("get-award-anonymous-month", formData)
      .subscribe({
        next: (response: any) => {
          switch (parseInt(response.status)) {
            case 1:
              this.router
                .navigate(["/" + this.utils.getGameKey() + "/game-play"])
                .then((success) => {
                  if (success) {
                    const data = response.giftCode[0];
                    if (!data) {
                      this.popupService.closePopup();
                      this.popupService.showNotification(
                        "Thông tin không hợp lệ!"
                      );
                      this.isClick = false;
                      return;
                    }
                    localStorage.setItem("award", data.voucher);
                    localStorage.setItem("code", data.code);
                    localStorage.setItem("name", data.name);
                    localStorage.setItem("phone", formData.phone);
                    localStorage.setItem("isSpin", "can");
                    localStorage.setItem("nameUser", this.name);
                    localStorage.setItem("branch", this.branch.toString());
                    this.popupService.closePopup();
                  } else {
                    this.popupService.closePopup();
                    this.popupService.showNotification(
                      "Chuyển trang không thành công!"
                    );
                    return;
                  }
                })
                .catch((error) => {
                  this.popupService.closePopup();
                  this.popupService.showNotification(
                    "Chuyển trang không thành công!"
                  );
                  return;
                });
              break;
            case -2:
              this.popupService.closePopup();
              this.popupService.showNotification("Thông tin không hợp lệ!");
              break;
            case -4:
              const data = response.giftCode[0];
              if (!data) {
                this.popupService.closePopup();
                this.popupService.showNotification("Thông tin không hợp lệ!");
                this.isClick = false;
                return;
              }
              if (data.status != 1) {
                this.popupService.closePopup();
                this.popupService.showNotification(
                  "Số điện thoại đã hoàn thành chương trình trước đó!"
                );
                return;
              }

              this.router
                .navigate(["/" + this.utils.getGameKey() + "/game-play"])
                .then((success) => {
                  if (success) {
                    this.popupService.closePopup();
                    localStorage.setItem("award", data.voucher);
                    localStorage.setItem("code", data.code);
                    localStorage.setItem("name", data.name);
                    localStorage.setItem("phone", formData.phone);
                    localStorage.setItem("isSpin", "can");
                    localStorage.setItem("nameUser", this.name);
                    localStorage.setItem("branch", this.branch.toString());
                  } else {
                    this.popupService.closePopup();
                    this.popupService.showNotification(
                      "Chuyển trang không thành công!"
                    );
                    return;
                  }
                })
                .catch((error) => {
                  this.popupService.closePopup();
                  this.popupService.showNotification(
                    "Chuyển trang không thành công!"
                  );
                  return;
                });
              break;
            default:
              this.popupService.closePopup();
              this.popupService.showNotification("Thông tin không hợp lệ!");
              break;
          }
        },
        error: (error: any) => {
          this.popupService.closePopup();
          this.popupService.showNotification("Hệ thống đang bảo trì!");
          this.isClick = false;
        },
        complete: () => {
          this.isClick = false;
        },
      });
  }
}
