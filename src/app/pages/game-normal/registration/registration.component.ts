import { Component } from "@angular/core";
import { BRANCH_OPTION, BranchOption } from "../../../constants/branch-option";
import { CommonModule } from "@angular/common";
import { ActivatedRoute, Router } from "@angular/router";
import { PopupService } from "../../../helpers/popup.service";
import { FormsModule } from "@angular/forms";
import { UtilsService } from "../../../helpers/utils.service";

@Component({
  selector: "app-registration",
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: "./registration.component.html",
  styleUrls: ["./registration.component.scss"],
})
export class RegistrationComponent {
  name: string = "";
  phone: string = "";
  branch: string = "";

  branchOption: BranchOption[] = BRANCH_OPTION;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private popupService: PopupService,
    private utils: UtilsService
  ) {}

  phoneChange(event: string) {
    this.phone = this.utils.keyupPhoneChange(event);
  }

  onSubmit() {
    // <PERSON><PERSON>y dữ liệu từ các input
    const formData = {
      name: this.name,
      phone: this.phone,
      branch: this.branch,
    };

    console.log("Form data:", formData);

    this.popupService.showLoading();

    // Xử lý validate nếu cần
    if (!formData.name || !formData.phone || !formData.branch) {
      this.popupService.closePopup();
      this.popupService.showNotification("Vui lòng điền đầy đủ thông tin!");
      return;
    }

    const phoneCheck = this.utils.validatePhone(this.phone);
    if (phoneCheck.status !== 0) {
      this.popupService.closePopup();
      this.popupService.showNotification("Vui lòng điền đầy đủ thông tin!");
      return;
    }

    this.router.navigate(["game-play"], { relativeTo: this.route });
  }
}
