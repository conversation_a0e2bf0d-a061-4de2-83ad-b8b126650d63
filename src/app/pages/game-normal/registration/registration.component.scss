.main-layout {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--background-color, #f5f5f5);
  .form-container {
    width: 100vw;
    max-width: 400px;
    height: 850px;
    margin: 0;
    border-radius: 0;
    box-shadow: none;
    background-image: url("/images/game1/background.png");
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    display: flex;
    flex-direction: column;
    justify-content: center;
    .form-header {
      height: 40%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      .form-header-top {
        height: 80%;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: row;
        .form-header-top-left {
          height: 100%;
          width: 20%;
          .form-header-top-left-img {
            width: 150%;
            height: 100%;
            object-fit: contain;
            margin-top: -12px;
          }
        }
        .form-header-top-center {
          height: 100%;
          width: 60%;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;
          .form-header-top-center-logo {
            height: 60%;
            width: 100%;
            display: flex;
            align-items: end;
            justify-content: center;
            .form-header-top-center-logo-img {
              width: 85%;
              object-fit: contain;
            }
          }
          .form-header-top-center-banner {
            height: 40%;
            width: 100%;
            .form-header-top-center-banner-img {
              width: 100%;
              height: 100%;
              object-fit: contain;
            }
          }
        }
        .form-header-top-right {
          height: 100%;
          width: 20%;
        }
      }
      .form-header-bottom {
        height: 20%;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        .form-header-bottom-img {
          width: 96%;
          height: 100%;
          object-fit: contain;
          margin-right: 5px;
        }
      }
    }
    .form-body {
      height: 60%;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 2rem 1rem;
      .registration-form {
        width: 90%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        border-radius: 20px;
        border: 5px solid var(--main-white);
        .registration-form-title {
          font-size: 20px;
          margin-bottom: 2rem;
          color: var(--main-white);
          text-align: center;
          text-transform: uppercase;
        }
        .form-row {
          margin-bottom: 1.5rem;
          width: 90%;
        }

        .form-row input,
        .form-row select {
          width: 100%;
          height: 48px;
          padding: 0 16px;
          border: 2px solid rgba(255, 255, 255, 0.9);
          border-radius: 12px;
          font-size: 16px;
          background: transparent;
          color: var(--main-white);
          transition: all 0.2s ease;
          box-sizing: border-box;
          appearance: none;
        }

        .form-row input::placeholder {
          color: rgba(255, 255, 255, 0.6);
        }

        .form-row input:focus,
        .form-row select:focus {
          outline: none;
          border-color: var(--main-white);
          box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.2);
        }

        .form-row input:hover,
        .form-row select:hover {
          border-color: var(--main-white);
        }

        .form-row select {
          background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
          background-position: right 12px center;
          background-repeat: no-repeat;
          background-size: 16px;
          padding-right: 40px;
          cursor: pointer;
        }

        .form-actions {
          width: 90%;
          .form-actions-btn {
            width: 100%;
            object-fit: contain;
          }
        }
      }
    }
  }
}

@media (min-width: 600px) {
  .main-layout {
    .form-container {
      height: 850px;
      max-width: 400px;
      border-radius: 16px;
      box-shadow: 0 2px 16px rgba(0, 0, 0, 0.08);
      margin: 40px auto;
    }
  }
}
