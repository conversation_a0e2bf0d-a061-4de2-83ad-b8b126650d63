import { CommonModule } from "@angular/common";
import { Component } from "@angular/core";
import gsap from "gsap";
import { PopupService } from "../../../helpers/popup.service";
import {
  FULL_CIRCLE,
  SLICE,
  COUNT_RORATE,
  VARIABLE_PER_SLICE,
  DURATION_RORATE,
} from "../../../constants/config-spin";

@Component({
  selector: "app-game-play",
  standalone: true,
  imports: [CommonModule],
  templateUrl: "./game-play.component.html",
  styleUrl: "./game-play.component.scss",
})
export class GamePlayComponent {
  validSpin: boolean = true;
  isSpinning: boolean = false;
  private targetSlice: number = 3;

  constructor(private popupService: PopupService) {}

  startSpin() {
    console.log("startSpin");

    if (this.isSpinning) {
      return;
    }

    if (!this.validSpin) {
      this.popupService.showNotification("<PERSON>ạn đã hết lượt quay!");
      return;
    }

    this.validSpin = false;
    this.isSpinning = false;
    const anglePerSlice = FULL_CIRCLE / SLICE;
    const rotationAngle =
      this.targetSlice * anglePerSlice +
      FULL_CIRCLE * COUNT_RORATE +
      VARIABLE_PER_SLICE["ten"];

    gsap.to(".spin", {
      rotation: rotationAngle,
      duration: DURATION_RORATE,
      ease: "power2.inOut",
      onComplete: () => {
        this.isSpinning = true;
        this.validSpin = true;
      },
    });
  }
}
