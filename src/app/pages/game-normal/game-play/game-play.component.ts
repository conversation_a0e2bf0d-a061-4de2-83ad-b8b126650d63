import { CommonModule } from "@angular/common";
import { Component, OnInit } from "@angular/core";
import gsap from "gsap";
import { PopupService } from "../../../helpers/popup.service";
import {
  FULL_CIRCLE,
  SLICE,
  COUNT_RORATE,
  VARIABLE_PER_SLICE,
  DURATION_RORATE,
} from "../../../constants/config-spin";
import { GIFTS_CODE } from "../../../constants/gift";
import { Router } from "@angular/router";
import { UtilsService } from "../../../helpers/utils.service";
import { ApiService } from "../../../helpers/api.service";

@Component({
  selector: "app-game-play",
  standalone: true,
  imports: [CommonModule],
  templateUrl: "./game-play.component.html",
  styleUrl: "./game-play.component.scss",
})
export class GamePlayComponent implements OnInit {
  validSpin: boolean = true;
  isSpinning: boolean = false;
  private targetSlice: number = 0;

  phone: string = "";
  voucher: string = "";
  name: string = "";
  code: string = "";

  constructor(
    private popupService: PopupService,
    private router: Router,
    private utils: UtilsService,
    private apiService: ApiService
  ) {}

  ngOnInit() {
    const PHONE = localStorage.getItem("phone");
    const AWARD = localStorage.getItem("award");
    const CODE = localStorage.getItem("code");
    const NAME = localStorage.getItem("name");
    const IS_SPIN = localStorage.getItem("isSpin");
    if (PHONE && AWARD && IS_SPIN && CODE && NAME) {
      this.phone = PHONE;
      this.voucher = AWARD;
      this.name = NAME;
      this.code = CODE;
      this.targetSlice = GIFTS_CODE.find((gift) => gift.uid === AWARD)?.id || 0;
      this.validSpin = IS_SPIN == "can" ? true : false;
    } else {
      this.router.navigate(["/" + this.utils.getGameKey()]).then(() => {
        this.utils.clearLocalStorage();
      });
    }
  }

  startSpin() {
    if (this.isSpinning) {
      return;
    }

    if (!this.validSpin) {
      this.popupService.showNotification("Bạn đã hết lượt quay!");
      return;
    }

    this.validSpin = false;
    this.isSpinning = false;
    const plusPower = this.targetSlice == 8 || this.targetSlice == 9 ? 20 : 0;
    const anglePerSlice = FULL_CIRCLE / SLICE;
    const rotationAngle =
      this.targetSlice * anglePerSlice +
      FULL_CIRCLE * COUNT_RORATE +
      VARIABLE_PER_SLICE["ten"] -
      17 +
      plusPower;

    gsap.to(".spin", {
      rotation: rotationAngle,
      duration: DURATION_RORATE,
      ease: "power2.inOut",
      onComplete: () => {
        this.notificationGift();
      },
    });
  }

  notificationGift() {
    this.popupService.showLoading();

    const formData = {
      code: this.code,
      phone: this.phone,
      giftCode: [
        {
          code: this.code,
          voucher: this.voucher,
          name: this.name,
        },
      ],
    };

    this.apiService
      .confirmInformationGameOA("confirm-anonymous-month", formData)
      .subscribe({
        next: (response: any) => {
          switch (parseInt(response.status)) {
            case 1:
              const data = response.giftCode[0];
              if (!data) {
                this.popupService.closePopup();
                this.popupService.showNotification("Thông tin không hợp lệ!");
                return;
              }
              if (data.status == 1) {
                this.popupService.closePopup();
                this.router.navigate([
                  "/" + this.utils.getGameKey() + "/notification",
                ]);
              }
              break;
            case -2:
            case -3:
              this.popupService.showNotification("Thông tin không hợp lệ!");
              break;
            default:
              this.popupService.showNotification("Thông tin không hợp lệ!");
              break;
          }
        },
        error: (error: any) => {
          this.popupService.closePopup();
          this.popupService.showNotification("Hệ thống đang bảo trì!");
        },
        complete: () => {
          this.popupService.closePopup();
        },
      });
  }
}
