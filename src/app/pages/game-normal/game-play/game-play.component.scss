.main-layout {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--background-color, #f5f5f5);
  .form-container {
    width: 100vw;
    max-width: 400px;
    height: 850px;
    margin: 0;
    border-radius: 0;
    box-shadow: none;
    background-image: url("/images/game1/background.png");
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    display: flex;
    flex-direction: column;
    justify-content: center;
    .form-header {
      height: 40%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      .form-header-top {
        height: 80%;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: row;
        .form-header-top-left {
          height: 100%;
          width: 20%;
          .form-header-top-left-img {
            width: 150%;
            height: 100%;
            object-fit: contain;
            margin-top: -12px;
          }
        }
        .form-header-top-center {
          height: 100%;
          width: 60%;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;
          .form-header-top-center-logo {
            height: 60%;
            width: 100%;
            display: flex;
            align-items: end;
            justify-content: center;
            .form-header-top-center-logo-img {
              width: 85%;
              object-fit: contain;
            }
          }
          .form-header-top-center-banner {
            height: 40%;
            width: 100%;
            .form-header-top-center-banner-img {
              width: 100%;
              height: 100%;
              object-fit: contain;
            }
          }
        }
        .form-header-top-right {
          height: 100%;
          width: 20%;
        }
      }
      .form-header-bottom {
        height: 20%;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        .form-header-bottom-img {
          width: 96%;
          height: 100%;
          object-fit: contain;
          margin-right: 5px;
        }
      }
    }
    .form-body {
      height: 60%;
      display: flex;
      flex-direction: column;
      justify-content: start;
      align-items: center;
      .form-body-game {
        width: 380px;
        height: 400px;
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
        .form-body-game-sub-spin {
          position: absolute;
          top: 10%;
          left: 50%;
          transform: translate(-50%, -50%);
          z-index: 2;
          width: 10%;
        }
        .form-body-game-button-spin {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          z-index: 2;
          width: 24%;
          .form-body-game-button-spin-img {
            width: 100%;
            object-fit: contain;
          }
        }
        .form-body-game-spin {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 100%;
          z-index: 1;
        }
        .form-body-flash-img {
          position: absolute;
          bottom: 0%;
          left: 50%;
          transform: translate(-50%, 50%);
          width: 100%;
        }
      }
    }
  }
}

@media (min-width: 600px) {
  .main-layout {
    .form-container {
      height: 850px;
      max-width: 400px;
      border-radius: 16px;
      box-shadow: 0 2px 16px rgba(0, 0, 0, 0.08);
      margin: 40px auto;
    }
  }
}
