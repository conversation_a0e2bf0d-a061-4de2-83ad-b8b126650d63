<div class="check-code-container">
  <div class="form-wrapper">
    <h2 class="form-title">Kiểm tra mã code</h2>
    <form class="check-code-form" (ngSubmit)="onSubmit()">
      <div class="input-group">
        <label for="code" class="input-label">Nhập mã code:</label>
        <input
          type="text"
          id="code"
          name="code"
          class="code-input"
          [(ngModel)]="code"
          placeholder="Nhập mã code của bạn"
          required
        />
      </div>
      <button type="submit" class="check-button" [disabled]="!code">
        Kiểm tra code
      </button>
    </form>
  </div>
</div>
