import { Component, OnInit } from "@angular/core";
import { UtilsService } from "../../../helpers/utils.service";
import { Router } from "@angular/router";
import { GIFTS } from "../../../constants/gift";

@Component({
  selector: "app-notification-gift",
  standalone: true,
  imports: [],
  templateUrl: "./notification-gift.component.html",
  styleUrl: "./notification-gift.component.scss",
})
export class NotificationGiftComponent implements OnInit {
  nameGift: string = "";
  urlGift: string = "";

  award: string = "";
  phone: string = "";

  constructor(private utils: UtilsService, private router: Router) {}

  ngOnInit() {
    const PHONE = localStorage.getItem("phone");
    const AWARD = localStorage.getItem("award");
    if (PHONE && AWARD) {
      const GIFT = GIFTS.find((gift) => gift.uid === AWARD);
      if (GIFT) {
        this.nameGift = GIFT.name || "";
        this.urlGift = GIFT.url;
      }
    } else {
      this.router.navigate(["/" + this.utils.getGameOaKey()]).then(() => {
        this.utils.clearLocalStorage();
      });
    }
  }
}
