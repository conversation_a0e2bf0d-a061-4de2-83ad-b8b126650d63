import { Component, OnInit } from "@angular/core";
import { UtilsService } from "../../../helpers/utils.service";
import { Router } from "@angular/router";
import { Gift, GIFTS } from "../../../constants/gift";

@Component({
  selector: "app-notification-gift",
  standalone: true,
  imports: [],
  templateUrl: "./notification-gift.component.html",
  styleUrl: "./notification-gift.component.scss",
})
export class NotificationGiftComponent implements OnInit {
  gifts: Gift[] = GIFTS;
  nameGift: string = "";
  urlGift: string = "";

  award: string = "";
  phone: string = "";

  constructor(private utils: UtilsService, private router: Router) {}

  ngOnInit() {
    const PHONE = sessionStorage.getItem("phone");
    const AWARD = sessionStorage.getItem("award");
    if (PHONE && AWARD) {
      const ID_GIFT = parseInt(AWARD);
      const GIFT = this.gifts.find((gift) => gift.id === ID_GIFT);
      if (GIFT) {
        this.nameGift = GIFT.name || "";
        this.urlGift = GIFT.url;
      }
    } else {
      this.router.navigate(["/" + this.utils.getGameOaKey]).then(() => {
        this.utils.clearLocalStorage();
      });
    }
  }
}
