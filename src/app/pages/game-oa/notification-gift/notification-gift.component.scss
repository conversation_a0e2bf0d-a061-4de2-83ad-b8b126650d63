.main-layout {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--background-color, #f5f5f5);
  overflow: hidden;
  .form-container {
    width: 100vw;
    max-width: 400px;
    height: 850px;
    margin: 0;
    border-radius: 0;
    box-shadow: none;
    background-image: url("/images/game1/background-voucher.png");
    background-size: 100% 100%;
    background-position: center;
    background-repeat: no-repeat;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .form-container-header {
      height: 30%;
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: end;
      align-items: center;
      gap: 10px;
      .form-container-header-top-logo {
        width: 50%;
        height: auto;
        object-fit: contain;
      }
      .form-container-header-top-congras {
        width: 96%;
        height: auto;
        object-fit: contain;
      }
    }
    .form-container-body {
      height: 45%;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: row;
      .form-container-body-side {
        width: 25%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: end;
        .form-container-body-side-img {
          width: 100%;
          height: auto;
          object-fit: contain;
        }
        .box1 {
          width: 232%;
          transform: rotate(-10deg);
          margin-bottom: 86px;
        }
        .box2 {
          width: 236%;
          transform: rotate(10deg);
          margin-bottom: 120px;
          margin-right: 28px;
        }
      }
      .form-container-body-center {
        width: 140%;
        height: 90%;
        background-image: url("/images/game1/wrap-voucher.png");
        background-size: 100% 100%;
        background-position: center;
        background-repeat: no-repeat;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        gap: 10px;
        .title-gift {
          width: 90%;
          font-size: 18px;
          font-weight: 800;
          line-height: 1.5;
          text-align: center;
          color: var(--main-yellow);
        }
        .gift {
          width: 150px;
          height: 150px;
          border-radius: 10px;
        }
        .description-gift {
          color: var(--main-yellow);
          font-size: 20px;
          font-weight: 600;
          line-height: 1.5;
          text-align: center;
          margin-top: 30px;
        }
      }
    }
    .form-container-footer {
      height: 25%;
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: 10px;
      .form-container-footer-thanku {
        width: 96%;
        height: auto;
        object-fit: contain;
      }
      .form-container-footer-note {
        width: 90%;
        font-size: 10px;
        font-weight: 600;
        line-height: 1.5;
        text-align: center;
        color: var(--main-white);
      }
    }
  }
}

@media (min-width: 600px) {
  .main-layout {
    .form-container {
      height: 850px;
      max-width: 400px;
      border-radius: 16px;
      box-shadow: 0 2px 16px rgba(0, 0, 0, 0.08);
      margin: 40px auto;
    }
  }
}
