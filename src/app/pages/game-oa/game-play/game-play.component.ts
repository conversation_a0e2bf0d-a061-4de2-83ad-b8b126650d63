import { CommonModule } from "@angular/common";
import { Component, OnInit } from "@angular/core";
import gsap from "gsap";
import { PopupService } from "../../../helpers/popup.service";
import {
  FULL_CIRCLE,
  SLICE,
  COUNT_RORATE,
  VARIABLE_PER_SLICE,
  DURATION_RORATE,
} from "../../../constants/config-spin";
import { ActivatedRoute, Router } from "@angular/router";
import { UtilsService } from "../../../helpers/utils.service";
import { ApiService } from "../../../helpers/api.service";
import { GIFTS } from "../../../constants/gift";

@Component({
  selector: "app-game-play",
  standalone: true,
  imports: [CommonModule],
  templateUrl: "./game-play.component.html",
  styleUrl: "./game-play.component.scss",
})
export class GamePlayComponent implements OnInit {
  validSpin: boolean = false;
  isSpinning: boolean = false;
  private targetSlice: number = 0;

  phone: string = "";
  voucher: string = "";
  name: string = "";
  code: string = "";

  constructor(
    private popupService: PopupService,
    private utils: UtilsService,
    private router: Router,
    private apiService: ApiService
  ) {}

  ngOnInit() {
    const PHONE = localStorage.getItem("phone");
    const AWARD = localStorage.getItem("award");
    const CODE = localStorage.getItem("code");
    const NAME = localStorage.getItem("name");
    const IS_SPIN = localStorage.getItem("isSpin");
    if (PHONE && AWARD && IS_SPIN && CODE && NAME) {
      this.phone = PHONE;
      this.voucher = AWARD;
      this.name = NAME;
      this.code = CODE;

      this.targetSlice = GIFTS.find((gift) => gift.uid === AWARD)?.id || 0;

      this.validSpin = IS_SPIN == "can" ? true : false;
    } else {
      this.router.navigate(["/" + this.utils.getGameOaKey()]).then(() => {
        this.utils.clearLocalStorage();
      });
    }
  }

  startSpin() {
    if (this.isSpinning || this.validSpin == false) {
      return;
    }

    this.validSpin = false;
    this.isSpinning = true;

    const anglePerSlice = FULL_CIRCLE / SLICE;
    const rotationAngle =
      this.targetSlice * anglePerSlice +
      FULL_CIRCLE * COUNT_RORATE +
      VARIABLE_PER_SLICE["ten"];

    gsap.to(".spin", {
      rotation: rotationAngle,
      duration: DURATION_RORATE,
      ease: "power2.inOut",
      onComplete: () => {
        this.notificationGift();
      },
    });
  }

  notificationGift() {
    this.popupService.showLoading();

    const formData = {
      code: this.code,
      phone: this.phone,
      giftCode: [
        {
          code: this.code,
          voucher: this.voucher,
          name: this.name,
        },
      ],
    };

    this.apiService
      .confirmInformationGameOA("confirm-anonymous", formData)
      .subscribe({
        next: (response: any) => {
          switch (parseInt(response.status)) {
            case 1:
              const data = response.giftCode[0];
              if (!data) {
                this.popupService.closePopup();
                this.popupService.showNotification("Thông tin không hợp lệ!");
                return;
              }
              if (data.status == 1) {
                this.popupService.closePopup();
                this.router.navigate([
                  "/" + this.utils.getGameOaKey() + "/notification",
                ]);
              }
              break;
            case -2:
              this.popupService.showNotification("Thông tin không hợp lệ!");
              break;
            default:
              this.popupService.showNotification("Thông tin không hợp lệ!");
              break;
          }
        },
        error: (error: any) => {
          this.popupService.closePopup();
          this.popupService.showNotification("Hệ thống đang bảo trì!");
        },
        complete: () => {
          this.popupService.closePopup();
        },
      });
  }
}
