import { CommonModule } from "@angular/common";
import { Component, OnInit } from "@angular/core";
import gsap from "gsap";
import { PopupService } from "../../../helpers/popup.service";
import {
  FULL_CIRCLE,
  SLICE,
  COUNT_RORATE,
  VARIABLE_PER_SLICE,
  DURATION_RORATE,
} from "../../../constants/config-spin";
import { ActivatedRoute, Router } from "@angular/router";
import { UtilsService } from "../../../helpers/utils.service";
import { ApiService } from "../../../helpers/api.service";

@Component({
  selector: "app-game-play",
  standalone: true,
  imports: [CommonModule],
  templateUrl: "./game-play.component.html",
  styleUrl: "./game-play.component.scss",
})
export class GamePlayComponent implements OnInit {
  validSpin: boolean = false;
  isSpinning: boolean = false;
  private targetSlice: number = 3;

  award: string = "";
  phone: string = "";

  constructor(
    private popupService: PopupService,
    private utils: UtilsService,
    private router: Router,
    private route: ActivatedRoute,
    private apiService: ApiService
  ) {}

  ngOnInit() {
    const PHONE = localStorage.getItem("phone");
    const AWARD = localStorage.getItem("award");
    const IS_SPIN = localStorage.getItem("isSpin");
    if (PHONE && AWARD && IS_SPIN) {
      this.phone = PHONE;
      this.award = AWARD;
      this.validSpin = IS_SPIN == "can" ? true : false;
    } else {
      this.router.navigate(["/" + this.utils.getGameOaKey]).then(() => {
        this.utils.clearLocalStorage();
      });
    }
  }

  startSpin() {
    if (this.isSpinning || this.validSpin == false) {
      return;
    }

    this.validSpin = false;
    this.isSpinning = true;

    const anglePerSlice = FULL_CIRCLE / SLICE;
    const rotationAngle =
      this.targetSlice * anglePerSlice +
      FULL_CIRCLE * COUNT_RORATE +
      VARIABLE_PER_SLICE["ten"];

    gsap.to(".spin", {
      rotation: rotationAngle,
      duration: DURATION_RORATE,
      ease: "power2.inOut",
      onComplete: () => {
        this.isSpinning = false;
        this.validSpin = false;
      },
    });
  }

  notificationGift() {
    this.popupService.showLoading();
    this.apiService
      .confirmInformationGameOA("", {
        phone: this.phone,
        award: this.award,
      })
      .subscribe({
        next: (response: any) => {
          switch (parseInt(response.status)) {
            case 1:
              this.popupService.closePopup();
              this.router.navigate(["notification"], {
                relativeTo: this.route,
              });
              break;
            case -2:
              this.popupService.showNotification("Số điện thoại không hợp lệ!");
              break;
            case -3:
              this.popupService.showNotification("Số điện thoại chưa đăng ký tham gia chương trình!");
              break;
            default:
              this.popupService.showNotification("Thông tin không hợp lệ!");
              break;
          }
        },
        error: (error: any) => {
          this.popupService.closePopup();
          this.popupService.showNotification("Hệ thống đang bảo trì!");
        },
        complete: () => {
          this.popupService.closePopup();
        },
      });
  }
}
