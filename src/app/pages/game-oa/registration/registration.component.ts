import { Component } from "@angular/core";
import { BRANCH_OPTION, BranchOption } from "../../../constants/branch-option";
import { CommonModule } from "@angular/common";
import { ActivatedRoute, Router } from "@angular/router";
import { PopupService } from "../../../helpers/popup.service";
import { FormsModule } from "@angular/forms";
import { UtilsService } from "../../../helpers/utils.service";
import { ApiService } from "../../../helpers/api.service";

@Component({
  selector: "app-registration",
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: "./registration.component.html",
  styleUrls: ["./registration.component.scss"],
})
export class RegistrationComponent {
  name: string = "";
  phone: string = "";
  branch: string = "";
  isClick: boolean = false;

  branchOption: BranchOption[] = BRANCH_OPTION;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private popupService: PopupService,
    private utils: UtilsService,
    private apiService: ApiService
  ) {}

  phoneChange(event: string) {
    this.phone = this.utils.keyupPhoneChange(event);
  }

  onSubmit() {
    this.popupService.showLoading();
    if (this.isClick) {
      return;
    }
    this.isClick = true;

    // Lấy dữ liệu từ các input
    const formData = {
      name: this.name,
      phone: this.phone,
      branch: this.branch,
    };

    // Xử lý validate nếu cần
    if (!formData.name || !formData.phone || !formData.branch) {
      this.popupService.closePopup();
      this.popupService.showNotification("Vui lòng điền đầy đủ thông tin!");
      this.isClick = false;
      return;
    }

    const phoneCheck = this.utils.validatePhone(this.phone);
    if (phoneCheck.status !== 0) {
      this.popupService.closePopup();
      this.popupService.showNotification("Vui lòng điền đầy đủ thông tin!");
      this.isClick = false;
      return;
    }

    formData.phone = phoneCheck.phone;
    this.apiService.verifyInformationGameOA("", formData).subscribe({
      next: (response: any) => {
        switch (parseInt(response.status)) {
          case 1:
            this.router
              .navigate(["notification"], { relativeTo: this.route })
              .then(() => {
                localStorage.setItem("award", response.data.award);
                localStorage.setItem("phone", formData.phone);
                localStorage.setItem("isSpin", "can");
              });
            break;
          case -2:
          case -3:
            this.popupService.closePopup();
            this.popupService.showNotification("Số điện thoại không hợp lệ!");
            break;
          case -4:
            this.popupService.showNotification(
              "Số điện thoại đã hoàn thành chương trình trước đó!"
            );
            break;
          default:
            this.popupService.closePopup();
            this.popupService.showNotification("Thông tin không hợp lệ!");
            break;
        }
      },
      error: (error: any) => {
        this.popupService.closePopup();
        this.popupService.showNotification("Hệ thống đang bảo trì!");
        this.isClick = false;
      },
      complete: () => {
        this.popupService.closePopup();
        this.isClick = false;
      },
    });
  }
}
