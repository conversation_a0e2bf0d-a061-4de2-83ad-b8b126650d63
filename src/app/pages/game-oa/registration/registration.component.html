<div class="main-layout">
  <div class="form-container">
    <div class="form-header">
      <div class="form-header-top">
        <div class="form-header-top-left">
          <img
            src="/images/game1/sub-banner.png"
            alt=""
            class="form-header-top-left-img"
          />
        </div>
        <div class="form-header-top-center">
          <div class="form-header-top-center-logo">
            <img
              src="/images/game1/logo.png"
              alt=""
              class="form-header-top-center-logo-img"
            />
          </div>
          <div class="form-header-top-center-banner">
            <img
              src="/images/game1/tittle.png"
              alt=""
              class="form-header-top-center-banner-img"
            />
          </div>
        </div>
        <div class="form-header-top-right"></div>
      </div>
      <div class="form-header-bottom">
        <img
          src="/images/game1/vqmm.png"
          alt=""
          class="form-header-bottom-img"
        />
      </div>
    </div>
    <div class="form-body">
      <div class="registration-form">
        <h2 class="registration-form-title">Đi<PERSON>n thông tin tham gia</h2>
        <div class="form-row">
          <input
            id="name"
            type="text"
            #nameInput
            autocomplete="off"
            placeholder="Họ và tên"
            [(ngModel)]="name"
          />
        </div>
        <div class="form-row">
          <input
            id="phone"
            type="tel"
            #phoneInput
            autocomplete="off"
            placeholder="Số điện thoại"
            inputmode="numeric"
            pattern="[0-9]*"
            maxlength="11"
            [(ngModel)]="phone"
            (ngModelChange)="phoneChange($event)"
          />
        </div>
        <div class="form-row">
          <select id="branch" #branchSelect [(ngModel)]="branch" placeholder="Chi nhánh">
            <option *ngFor="let opt of branchOption" [value]="opt.value"
              >{{ opt.label }}</option
            >
          </select>
        </div>
        <div class="form-actions" (click)="onSubmit()">
          <img
            src="/images/game1/button-join.png"
            alt=""
            class="form-actions-btn"
          />
        </div>
      </div>
    </div>
  </div>
</div>
