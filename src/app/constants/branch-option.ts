export interface BranchOption {
  value: number;
  label: string;
}

export const BRANCH_OPTION: BranchOption[] = [
  { value: 0, label: '<PERSON> nhán<PERSON>' },
  { value: 1, label: '352 <PERSON><PERSON>, P12, <PERSON><PERSON> <PERSON>' },
  { value: 2, label: '186-188-190 <PERSON>, P2, <PERSON><PERSON>' },
  { value: 3, label: '91 <PERSON>, P. <PERSON>, Q1' },
  { value: 4, label: 'Số 1 <PERSON><PERSON><PERSON>, <PERSON><PERSON>, TP. Th<PERSON> Đ<PERSON>' },
  { value: 5, label: '2A <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, T<PERSON><PERSON>' }
];
